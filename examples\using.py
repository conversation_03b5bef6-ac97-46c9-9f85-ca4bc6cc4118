import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ccsm.core.model import ColorectalCancerMicrosimulationModel
from src.ccsm.modules.screening import ScreeningStrategy, ScreeningToolConfig
from src.ccsm.core.enums import ScreeningTool

# 1. 初始化模型
model = ColorectalCancerMicrosimulationModel(initial_population=10000)

# 2. 设置人口参数
age_distribution = {50: 0.1, 55: 0.15, 60: 0.2, 65: 0.25, 70: 0.2, 75: 0.1}
model.setup_population(age_distribution)

# 3. 添加筛查策略
strategy = ScreeningStrategy(
    name="fit_then_colonoscopy",
    tool_configs=[
        ScreeningToolConfig(
            tool=ScreeningTool.FIT,
            start_age=50,
            end_age=75,
            interval=1.0,
            compliance_rate=0.70,
            follow_up_compliance_rate=0.75
        )
    ],
    sequential=True
)
model.add_screening_strategy(strategy)

# 4. 运行模拟
results = model.run_simulation(years=20, screening_strategy="fit_then_colonoscopy")

# 5. 查看结果
print("模拟完成！")
print(f"结果类型: {type(results)}")
if isinstance(results, dict):
    for key, value in results.items():
        print(f"{key}: {value}")
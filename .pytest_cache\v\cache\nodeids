["tests/test_enhanced_screening.py::test_age_range_validation", "tests/test_enhanced_screening.py::test_backward_compatibility", "tests/test_enhanced_screening.py::test_enhanced_screening_strategy", "tests/test_enhanced_screening.py::test_enhanced_statistics", "tests/test_enhanced_screening.py::test_screening_tool_config", "tests/test_enhanced_screening.py::test_sequential_screening_logic", "tests/test_numerical_compliance.py::test_compliance_rate_consistency", "tests/test_numerical_compliance.py::test_numerical_compliance_recording", "tests/test_numerical_compliance.py::test_population_compliance_statistics", "tests/test_numerical_compliance.py::test_sequential_screening_with_numerical_compliance"]
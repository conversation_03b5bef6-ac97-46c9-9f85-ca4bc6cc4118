# 项目结构说明

## 重构后的标准化项目结构

```
Colorectal_Cancer_Screening_Microsimulation_Model/
├── src/                          # 源代码目录
│   └── ccsm/                     # 主要Python包
│       ├── __init__.py
│       ├── config/               # 配置文件目录
│       │   ├── __init__.py
│       │   ├── config_manager.py
│       │   ├── screening_parameters.json
│       │   ├── screening_strategies.xlsx
│       │   ├── default_benchmarks.xlsx
│       │   ├── lifetable.xlsx
│       │   └── population.xlsx
│       ├── core/                 # 核心模块
│       │   ├── __init__.py
│       │   ├── enums.py
│       │   ├── individual.py
│       │   └── model.py
│       ├── data/                 # 数据处理模块
│       │   ├── __init__.py
│       │   ├── data_loader.py
│       │   └── data_manager.py
│       ├── modules/              # 功能模块
│       │   ├── __init__.py
│       │   ├── calibration.py
│       │   ├── config_loader.py
│       │   ├── disease.py
│       │   ├── economics.py
│       │   ├── population.py
│       │   └── screening.py
│       ├── tests/                # 测试目录
│       │   ├── __init__.py
│       │   ├── run_tests.py
│       │   ├── unit/             # 单元测试
│       │   │   ├── __init__.py
│       │   │   ├── test_*.py
│       │   ├── integration/      # 集成测试
│       │   │   ├── __init__.py
│       │   │   ├── simple_test.py
│       │   │   └── validate_screening_intervals.py
│       │   └── debug/            # 调试脚本
│       │       ├── __init__.py
│       │       └── debug_*.py
│       ├── ui/                   # 用户界面
│       │   ├── __init__.py
│       │   └── cli.py
│       └── utils/                # 工具模块
│           └── __init__.py
├── data/                         # 运行时数据目录
│   ├── README.md
│   ├── parameters/               # 参数文件
│   ├── population/               # 人口数据
│   ├── results/                  # 模拟结果
│   ├── exports/                  # 导出文件
│   └── legacy/                   # 遗留数据
├── examples/                     # 示例脚本
│   ├── basic_simulation.py
│   ├── custom_lifetable_example.py
│   ├── data_integrated_simulation.py
│   ├── enhanced_screening_demo.py
│   ├── final_verification.py
│   ├── simple_config_example.py
│   ├── test_config_loading.py
│   ├── test_unified_config.py
│   └── using.py
├── scripts/                      # 脚本目录
│   └── legacy/                   # 遗留脚本
├── docs/                         # 文档目录
│   ├── custom_lifetable_integration.md
│   ├── deployment_guide.md
│   ├── enhanced_screening_features.md
│   ├── screening_config_usage.md
│   └── user_manual.md
├── .gitignore                    # Git忽略文件
├── requirements.txt              # Python依赖
├── setup.py                      # 安装脚本
└── README.md                     # 项目说明
```

## 重构改进

### 1. 统一配置管理
- 所有配置文件集中到 `src/ccsm/config/`
- 消除了重复的config目录
- 统一配置加载器自动使用新路径

### 2. 规范化测试结构
- 单元测试：`src/ccsm/tests/unit/`
- 集成测试：`src/ccsm/tests/integration/`
- 调试脚本：`src/ccsm/tests/debug/`

### 3. 清理临时文件
- 移除虚拟环境目录
- 清理__pycache__和.egg-info
- 添加.gitignore防止临时文件提交

### 4. 明确数据目录用途
- `src/ccsm/data/`：数据处理Python模块
- `data/`：运行时数据存储
- 添加README说明区别

### 5. 脚本文件重组
- 示例脚本移至 `examples/`
- 遗留代码移至 `scripts/legacy/`
- 遗留数据移至 `data/legacy/`

## 使用说明

### 配置文件路径
所有配置文件现在位于 `src/ccsm/config/`，使用时指定：
```python
config_dir="src/ccsm/config"
```

### 运行测试
```bash
# 运行所有测试
python src/ccsm/tests/run_tests.py

# 运行单元测试
python -m pytest src/ccsm/tests/unit/

# 运行集成测试
python -m pytest src/ccsm/tests/integration/
```

### 运行示例
```bash
python examples/simple_config_example.py
python examples/test_unified_config.py
```

这个重构确保了项目结构的标准化和可维护性。

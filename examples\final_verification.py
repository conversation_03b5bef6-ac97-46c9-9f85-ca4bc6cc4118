#!/usr/bin/env python3
"""
最终验证脚本
验证统一配置加载器的所有功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ccsm.core.model import ColorectalCancerMicrosimulationModel, ModelConfiguration
from src.ccsm.modules.config_loader import UnifiedConfigManager


def verify_config_files():
    """验证配置文件是否存在"""
    print("=" * 60)
    print("配置文件验证")
    print("=" * 60)
    
    config_dir = Path("config")
    required_files = [
        "screening_parameters.json",
        "screening_strategies.xlsx", 
        "default_benchmarks.xlsx",
        "lifetable.xlsx",
        "population.xlsx"
    ]
    
    all_exist = True
    for file_name in required_files:
        file_path = config_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} - 文件不存在")
            all_exist = False
    
    return all_exist


def verify_unified_config_loader():
    """验证统一配置加载器"""
    print("\n" + "=" * 60)
    print("统一配置加载器验证")
    print("=" * 60)
    
    config_manager = UnifiedConfigManager("config")
    
    # 测试各种数据加载
    tests = [
        ("筛查参数", lambda: config_manager.initialize_from_config()),
        ("基准数据", lambda: config_manager.load_benchmark_data()),
        ("寿命表数据", lambda: config_manager.load_lifetable_data()),
        ("人口构成数据", lambda: config_manager.load_population_data()),
        ("筛查性能数据", lambda: config_manager.load_screening_data()),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                if test_name == "筛查参数":
                    params, strategies = result
                    results[test_name] = f"✅ 参数: {params is not None}, 策略: {len(strategies)}个"
                elif isinstance(result, dict):
                    results[test_name] = f"✅ {len(result)}项"
                elif isinstance(result, list):
                    results[test_name] = f"✅ {len(result)}个"
                else:
                    results[test_name] = "✅ 已加载"
            else:
                results[test_name] = "❌ 空结果"
        except Exception as e:
            results[test_name] = f"❌ 错误: {str(e)[:50]}"
    
    for test_name, result in results.items():
        print(f"{test_name}: {result}")
    
    return all("✅" in result for result in results.values())


def verify_model_integration():
    """验证模型集成"""
    print("\n" + "=" * 60)
    print("模型集成验证")
    print("=" * 60)
    
    # 测试1: 模型初始化时加载配置
    print("\n1. 模型初始化时加载配置...")
    config = ModelConfiguration(
        initial_population=1000,
        load_config_from_files=True,
        config_dir="config"
    )
    
    try:
        model1 = ColorectalCancerMicrosimulationModel(config=config)
        strategies_count = len(model1.list_available_strategies())
        print(f"✅ 模型初始化成功，策略数量: {strategies_count}")
        init_success = True
    except Exception as e:
        print(f"❌ 模型初始化失败: {e}")
        init_success = False
    
    # 测试2: 运行时加载配置
    print("\n2. 运行时加载配置...")
    try:
        model2 = ColorectalCancerMicrosimulationModel(initial_population=1000)
        initial_count = len(model2.list_available_strategies())
        
        model2.load_screening_config_from_files(
            params_file="screening_parameters.json",
            strategies_file="screening_strategies.xlsx"
        )
        
        final_count = len(model2.list_available_strategies())
        print(f"✅ 运行时加载成功，策略数量: {initial_count} → {final_count}")
        runtime_success = True
    except Exception as e:
        print(f"❌ 运行时加载失败: {e}")
        runtime_success = False
    
    return init_success and runtime_success


def verify_data_loading():
    """验证数据加载功能"""
    print("\n" + "=" * 60)
    print("数据加载功能验证")
    print("=" * 60)
    
    config_manager = UnifiedConfigManager("config")
    
    # 加载所有数据
    all_data = config_manager.load_all_data()
    
    print("\n数据加载结果:")
    for data_type, data in all_data.items():
        if data:
            if isinstance(data, dict):
                print(f"✅ {data_type}: {len(data)} 项")
            elif isinstance(data, list):
                print(f"✅ {data_type}: {len(data)} 个")
            else:
                print(f"✅ {data_type}: 已加载")
        else:
            print(f"❌ {data_type}: 未加载")
    
    # 验证关键数据
    success_checks = [
        all_data['benchmark_data'],
        all_data['lifetable_data'],
        all_data['population_data'],
        all_data['screening_parameters'],
        all_data['screening_strategies']
    ]
    
    return all(success_checks)


def main():
    """主验证函数"""
    print("🔍 开始最终验证...")
    
    # 执行所有验证
    tests = [
        ("配置文件", verify_config_files),
        ("统一配置加载器", verify_unified_config_loader),
        ("模型集成", verify_model_integration),
        ("数据加载功能", verify_data_loading)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n❌ {test_name}验证异常: {e}")
            results[test_name] = False
    
    # 总结结果
    print("\n" + "=" * 60)
    print("验证结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 通过")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有验证通过！")
        print("\n✅ 统一配置加载器功能完整：")
        print("   • 所有数据文件统一到config目录")
        print("   • 筛查参数和策略配置正常加载")
        print("   • 基准数据、寿命表、人口数据正常加载")
        print("   • 模型初始化时自动加载配置")
        print("   • 运行时动态加载配置")
        print("   • 与主模型完全集成")
        return True
    else:
        print("\n⚠️ 部分验证失败，请检查相关功能")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 验证过程中出现严重错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

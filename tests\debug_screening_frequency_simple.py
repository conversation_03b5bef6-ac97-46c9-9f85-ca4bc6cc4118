"""
简单测试筛查频率逻辑
验证筛查间隔是否正确工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.ccsm.core.individual import Individual
from src.ccsm.core.enums import Gender, CancerStage
from src.ccsm.modules.screening import ScreeningModule, ScreeningParameters


def test_screening_intervals_simple():
    """简单测试筛查间隔"""
    print("=== 简单测试筛查间隔 ===\n")
    
    # 创建筛查模块
    screening_params = ScreeningParameters()
    screening_module = ScreeningModule(screening_params)
    screening_module.create_predefined_strategies()
    
    # 测试策略
    strategies = {
        "fit_6_months": 0.5,   # 6个月
        "annual_fit": 1.0,     # 1年
        "fit_1_5_years": 1.5,  # 1.5年
        "biennial_fit": 2.0    # 2年
    }
    
    print("理论筛查次数计算（10年期间）:")
    for strategy_name, interval in strategies.items():
        theoretical_count = int(10 / interval)
        print(f"{strategy_name}: 间隔{interval}年 → 理论{theoretical_count}次筛查")
    
    print("\n实际筛查次数测试:")
    
    for strategy_name, expected_interval in strategies.items():
        print(f"\n测试策略: {strategy_name}")
        
        # 创建测试个体
        individual = Individual(
            id=1,
            gender=Gender.MALE,
            birth_year=1970,
            current_age=50
        )
        
        strategy = screening_module.strategies[strategy_name]
        screening_times = []
        
        # 模拟10年，每季度检查
        for quarter in range(40):  # 10年 * 4季度
            current_year = 2020 + quarter / 4.0
            
            # 检查是否应该筛查
            should_screen = screening_module._should_screen_individual(
                individual, strategy, current_year
            )
            
            if should_screen:
                # 模拟筛查（不实际执行，只记录时间）
                screening_times.append(current_year)
                individual.last_screening_year = current_year
        
        print(f"  实际筛查次数: {len(screening_times)}")
        print(f"  筛查时间: {[f'{t:.2f}' for t in screening_times[:5]]}{'...' if len(screening_times) > 5 else ''}")
        
        # 计算实际间隔
        if len(screening_times) > 1:
            intervals = [screening_times[i+1] - screening_times[i] for i in range(len(screening_times)-1)]
            avg_interval = sum(intervals) / len(intervals)
            print(f"  平均间隔: {avg_interval:.2f}年")
            print(f"  期望间隔: {expected_interval}年")
            
            if abs(avg_interval - expected_interval) < 0.1:
                print("  ✅ 间隔正确")
            else:
                print("  ❌ 间隔异常")
        else:
            print("  ❌ 筛查次数异常")


def test_population_screening_frequency():
    """测试人群筛查频率"""
    print("\n=== 测试人群筛查频率 ===\n")
    
    from src.ccsm.core.model import ColorectalCancerMicrosimulationModel
    
    # 创建小规模模型
    model = ColorectalCancerMicrosimulationModel(initial_population=100)
    age_distribution = {50: 1.0}  # 全部50岁
    
    strategies_to_test = ["fit_6_months", "annual_fit", "biennial_fit"]
    
    for strategy_name in strategies_to_test:
        print(f"\n策略: {strategy_name}")
        
        # 重新设置人口
        model.setup_population(age_distribution, 0.5)
        
        total_screenings = 0
        quarterly_screenings = []
        
        # 模拟5年，每季度
        for quarter in range(20):  # 5年 * 4季度
            current_year = 2020 + quarter / 4.0
            
            # 执行人群筛查
            screening_stats = model.screening_module.screen_population(
                model.population_module.population, strategy_name, current_year
            )
            
            quarter_screenings_count = screening_stats.get('screened_population', 0)
            quarterly_screenings.append(quarter_screenings_count)
            total_screenings += quarter_screenings_count
        
        print(f"  总筛查人次: {total_screenings}")
        print(f"  平均每季度筛查: {total_screenings/20:.1f}人")
        
        # 显示前几个季度的筛查情况
        non_zero_quarters = [(i, count) for i, count in enumerate(quarterly_screenings) if count > 0]
        print(f"  有筛查的季度: {non_zero_quarters[:5]}{'...' if len(non_zero_quarters) > 5 else ''}")


if __name__ == '__main__':
    try:
        test_screening_intervals_simple()
        test_population_screening_frequency()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

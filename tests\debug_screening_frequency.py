"""
调试筛查频率和成本计算
分析为什么不同间隔的筛查成本相同
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.ccsm.core.model import ColorectalCancerMicrosimulationModel
from src.ccsm.core.individual import Individual
from src.ccsm.core.enums import Gender, CancerStage, ScreeningTool
from src.ccsm.modules.screening import ScreeningModule, ScreeningParameters
import pandas as pd


def debug_screening_intervals():
    """调试筛查间隔实现"""
    print("=== 调试筛查间隔实现 ===\n")
    
    # 创建筛查模块
    screening_params = ScreeningParameters()
    screening_module = ScreeningModule(screening_params)
    screening_module.create_predefined_strategies()  # 初始化默认策略
    
    # 创建测试个体
    individual = Individual(
        id=1,
        gender=Gender.MALE,
        birth_year=1970,
        current_age=50
    )
    
    # 测试不同策略的筛查间隔
    strategies_to_test = ["annual_fit", "biennial_fit", "fit_1_5_years", "fit_6_months"]
    
    print("策略间隔设置:")
    for strategy_name in strategies_to_test:
        strategy = screening_module.strategies[strategy_name]
        print(f"{strategy_name}: 间隔 {strategy.intervals} 年")
    
    print("\n=== 模拟10年筛查频率 ===")
    
    for strategy_name in strategies_to_test:
        print(f"\n策略: {strategy_name}")
        strategy = screening_module.strategies[strategy_name]
        
        # 重置个体状态
        test_individual = Individual(
            id=1,
            gender=Gender.MALE,
            birth_year=1970,
            current_age=50
        )
        
        screening_years = []
        total_cost = 0.0
        
        # 模拟10年，每季度检查
        for quarter in range(40):  # 10年 * 4季度
            current_year = 2020 + quarter / 4.0
            
            # 检查是否应该筛查
            should_screen = screening_module._should_screen_individual(
                test_individual, strategy, current_year
            )
            
            if should_screen:
                # 执行筛查
                records = screening_module.perform_screening(
                    test_individual, strategy_name, current_year
                )
                
                if records:
                    screening_years.append(current_year)
                    total_cost += sum(record.cost for record in records)
                    print(f"  筛查时间: {current_year:.2f}, 成本: {records[0].cost}")
        
        print(f"总筛查次数: {len(screening_years)}")
        print(f"总成本: ¥{total_cost:,.0f}")
        print(f"筛查年份: {[f'{y:.2f}' for y in screening_years[:5]]}{'...' if len(screening_years) > 5 else ''}")


def debug_population_screening():
    """调试人群筛查成本"""
    print("\n=== 调试人群筛查成本 ===\n")
    
    # 创建小规模模型
    model = ColorectalCancerMicrosimulationModel(initial_population=100)
    
    # 设置人口分布
    age_distribution = {50: 1.0}  # 全部50岁
    model.setup_population(age_distribution, 0.5)
    
    strategies_to_test = ["annual_fit", "biennial_fit"]
    
    for strategy_name in strategies_to_test:
        print(f"\n=== 策略: {strategy_name} ===")
        
        # 重置人口状态
        for individual in model.population_module.population:
            individual.screening_history.clear()
            individual.last_screening_year = None
            individual.total_screening_cost = 0.0
        
        total_screenings = 0
        total_cost = 0.0
        
        # 模拟5年，每季度
        for quarter in range(20):  # 5年 * 4季度
            current_year = 2020 + quarter / 4.0
            
            # 执行人群筛查
            screening_stats = model.screening_module.screen_population(
                model.population_module.population, strategy_name, current_year
            )
            
            quarter_screenings = screening_stats.get('screened_population', 0)
            quarter_cost = screening_stats.get('total_cost', 0)
            
            if quarter_screenings > 0:
                print(f"  Q{quarter+1} ({current_year:.2f}): {quarter_screenings}人筛查, 成本¥{quarter_cost:,.0f}")
                total_screenings += quarter_screenings
                total_cost += quarter_cost
        
        print(f"总筛查人次: {total_screenings}")
        print(f"总成本: ¥{total_cost:,.0f}")
        
        # 检查个体筛查历史
        sample_individual = model.population_module.population[0]
        print(f"样本个体筛查次数: {len(sample_individual.screening_history)}")
        print(f"样本个体总成本: ¥{sample_individual.total_screening_cost:,.0f}")


def analyze_cost_calculation():
    """分析成本计算逻辑"""
    print("\n=== 分析成本计算逻辑 ===\n")
    
    # 创建模型
    model = ColorectalCancerMicrosimulationModel(initial_population=10)
    age_distribution = {50: 1.0}
    model.setup_population(age_distribution, 0.5)
    
    # 运行两个策略
    strategies = ["annual_fit", "biennial_fit"]
    
    for strategy_name in strategies:
        print(f"\n策略: {strategy_name}")
        
        # 重置人口
        for individual in model.population_module.population:
            individual.screening_history.clear()
            individual.treatment_history.clear()
            individual.last_screening_year = None
            individual.total_screening_cost = 0.0
            individual.total_treatment_cost = 0.0
        
        # 运行模拟
        result = model.run_simulation(
            years=5,
            screening_strategy=strategy_name,
            show_progress=False
        )
        
        # 分析结果
        economics = result.get('economics_outcome', {})
        print(f"  经济学模块计算的总成本: ¥{economics.get('total_cost', 0):,.0f}")
        print(f"  筛查成本: ¥{economics.get('total_screening_cost', 0):,.0f}")
        print(f"  治疗成本: ¥{economics.get('total_treatment_cost', 0):,.0f}")
        
        # 检查个体成本累计
        total_individual_screening_cost = sum(ind.total_screening_cost for ind in model.population_module.population)
        total_individual_treatment_cost = sum(ind.total_treatment_cost for ind in model.population_module.population)
        
        print(f"  个体累计筛查成本: ¥{total_individual_screening_cost:,.0f}")
        print(f"  个体累计治疗成本: ¥{total_individual_treatment_cost:,.0f}")
        
        # 检查筛查记录
        total_screening_records = sum(len(ind.screening_history) for ind in model.population_module.population)
        print(f"  总筛查记录数: {total_screening_records}")
        
        # 检查样本个体
        sample_individual = model.population_module.population[0]
        print(f"  样本个体筛查记录: {len(sample_individual.screening_history)}")
        if sample_individual.screening_history:
            print(f"  筛查年份: {[record.year for record in sample_individual.screening_history]}")


if __name__ == '__main__':
    try:
        debug_screening_intervals()
        debug_population_screening()
        analyze_cost_calculation()
    except Exception as e:
        print(f"调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

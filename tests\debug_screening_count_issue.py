"""
深度调试筛查次数问题
分析为什么频繁筛查策略的总筛查次数反而更少
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.ccsm.core.model import ColorectalCancerMicrosimulationModel
import pandas as pd


def debug_screening_count_detailed():
    """详细调试筛查次数问题"""
    print("=== 详细调试筛查次数问题 ===\n")

    # 创建小规模模型便于分析
    model = ColorectalCancerMicrosimulationModel(initial_population=200)
    age_distribution = {50: 1.0}  # 全部50岁开始
    
    strategies_to_test = ["fit_6_months", "annual_fit", "biennial_fit"]
    
    for strategy_name in strategies_to_test:
        print(f"\n=== 策略: {strategy_name} ===")
        
        # 重新设置人口
        model.setup_population(age_distribution, 0.5)
        
        # 跟踪详细信息
        total_screenings = 0
        alive_population_over_time = []
        eligible_population_over_time = []
        screening_events_over_time = []
        
        # 模拟10年，每季度
        for quarter in range(40):  # 10年 * 4季度
            current_year = 2020 + quarter / 4.0
            
            # 获取存活人口
            alive_population = [ind for ind in model.population_module.population if ind.alive]
            alive_count = len(alive_population)
            
            # 获取符合筛查条件的人口
            strategy = model.screening_module.strategies[strategy_name]
            eligible_population = []
            for individual in alive_population:
                age = individual.get_age_at_year(current_year)
                if strategy.start_age <= age <= strategy.end_age:
                    eligible_population.append(individual)
            
            eligible_count = len(eligible_population)
            
            # 执行筛查
            screening_stats = model.screening_module.screen_population(
                alive_population, strategy_name, current_year
            )
            
            screened_count = screening_stats.get('screened_population', 0)
            total_screenings += screened_count
            
            # 记录数据
            alive_population_over_time.append(alive_count)
            eligible_population_over_time.append(eligible_count)
            screening_events_over_time.append(screened_count)
            
            # 进行疾病进展（模拟模型的正常流程）
            for individual in alive_population:
                model.disease_module.progress_disease(individual, current_year)
            
            # 更新人口（每年第一季度）
            if quarter % 4 == 0:
                model.population_module.update_population(int(current_year))
        
        # 分析结果
        print(f"总筛查次数: {total_screenings}")
        print(f"最终存活人口: {alive_population_over_time[-1]}")
        print(f"最终符合筛查条件人口: {eligible_population_over_time[-1]}")
        
        # 计算平均值
        avg_alive = sum(alive_population_over_time) / len(alive_population_over_time)
        avg_eligible = sum(eligible_population_over_time) / len(eligible_population_over_time)
        avg_screened_per_quarter = sum(screening_events_over_time) / len(screening_events_over_time)
        
        print(f"平均存活人口: {avg_alive:.1f}")
        print(f"平均符合筛查条件人口: {avg_eligible:.1f}")
        print(f"平均每季度筛查人数: {avg_screened_per_quarter:.1f}")
        
        # 分析筛查频率
        non_zero_screenings = [x for x in screening_events_over_time if x > 0]
        print(f"有筛查活动的季度数: {len(non_zero_screenings)}")
        
        if non_zero_screenings:
            print(f"有筛查时的平均筛查人数: {sum(non_zero_screenings)/len(non_zero_screenings):.1f}")
        
        # 显示前10个季度的详细情况
        print("前10个季度详情:")
        print("季度  年份    存活  符合条件  筛查人数")
        for i in range(min(10, len(alive_population_over_time))):
            year = 2020 + i / 4.0
            print(f"{i+1:2d}   {year:6.2f}  {alive_population_over_time[i]:3d}   {eligible_population_over_time[i]:3d}      {screening_events_over_time[i]:3d}")


def analyze_individual_screening_patterns():
    """分析个体筛查模式"""
    print("\n=== 分析个体筛查模式 ===\n")
    
    from src.ccsm.core.individual import Individual
    from src.ccsm.core.enums import Gender
    from src.ccsm.modules.screening import ScreeningModule, ScreeningParameters
    
    # 创建筛查模块
    screening_params = ScreeningParameters()
    screening_module = ScreeningModule(screening_params)
    screening_module.create_predefined_strategies()
    
    strategies_to_test = ["fit_6_months", "annual_fit", "biennial_fit"]
    
    for strategy_name in strategies_to_test:
        print(f"\n策略: {strategy_name}")
        
        # 创建测试个体
        individual = Individual(
            id=1,
            gender=Gender.MALE,
            birth_year=1970,
            current_age=50
        )
        
        strategy = screening_module.strategies[strategy_name]
        screening_count = 0
        
        # 模拟10年，每季度检查
        for quarter in range(40):  # 10年 * 4季度
            current_year = 2020 + quarter / 4.0
            current_age = individual.get_age_at_year(current_year)
            
            # 检查是否在筛查年龄范围内
            if not (strategy.start_age <= current_age <= strategy.end_age):
                continue
            
            # 检查是否应该筛查
            should_screen = screening_module._should_screen_individual(
                individual, strategy, current_year
            )
            
            if should_screen:
                screening_count += 1
                individual.last_screening_year = current_year
                print(f"  第{screening_count}次筛查: {current_year:.2f}年, 年龄{current_age:.1f}")
        
        print(f"总筛查次数: {screening_count}")


if __name__ == '__main__':
    try:
        analyze_individual_screening_patterns()
        debug_screening_count_detailed()
    except Exception as e:
        print(f"调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

# 增强筛查功能说明文档

## 概述

本次更新对结直肠癌筛查微观仿真模型的筛查模块进行了重大增强，实现了以下三个核心需求：

1. **贯序筛查策略中每个筛查需要单独录入起止年龄**
2. **统计筛查工具筛查次数和确诊性肠镜筛查次数**
3. **每个筛查工具需要有筛查依从性及筛查阳性后的接受肠镜筛查的依从性**

## 主要功能特性

### 1. 灵活的筛查工具配置

#### 新增 `ScreeningToolConfig` 类
```python
@dataclass
class ScreeningToolConfig:
    tool: ScreeningTool                         # 筛查工具
    start_age: int                              # 开始年龄
    end_age: int                                # 结束年龄
    interval: float                             # 筛查间隔（年）
    compliance_rate: Optional[float] = None     # 筛查依从性
    follow_up_compliance_rate: Optional[float] = None  # 阳性后肠镜依从性
```

#### 增强的 `ScreeningStrategy` 类
- 支持每个筛查工具独立的年龄范围配置
- 支持每个工具独立的依从性设置
- 保持向后兼容性，支持旧格式配置

### 2. 详细的筛查统计功能

#### 增强的筛查记录
```python
@dataclass
class ScreeningRecord:
    # 原有字段...
    follow_up_compliant: bool = False         # 后续依从性（布尔结果）
    follow_up_compliance_rate: float = 0.0    # 后续依从性概率（数值型）
    is_primary_screening: bool = True         # 是否为初筛
    is_diagnostic_colonoscopy: bool = False   # 是否为确诊性肠镜
    triggered_by_tool: Optional[str] = None   # 触发确诊性肠镜的工具
    screening_round: int = 1                  # 筛查轮次
    strategy_name: str = ""                   # 所属策略名称
```

#### 新增统计指标
- 按工具类型的筛查次数统计
- 确诊性肠镜筛查次数
- 初筛次数
- 后续依从率（实际执行后续检查的比例）
- 平均后续依从性概率（设置的依从性概率平均值）

### 3. 智能的贯序筛查逻辑

#### 数值型依从性处理
- **依从性概率存储**: 记录每个筛查的依从性概率值（0-1之间的数值）
- **概率驱动决策**: 基于依从性概率进行随机抽样，决定是否执行后续检查
- **统计分析**: 提供实际依从率和平均依从性概率两个指标

#### 自动确诊性肠镜触发
- FIT阳性时，基于依从性概率决定是否执行确诊性肠镜
- 乙状结直肠镜阳性时，基于依从性概率决定是否执行全结肠镜
- 自动记录触发关系和筛查类型

#### 灵活的年龄范围管理
- 支持不同工具的年龄范围重叠
- 贯序筛查逻辑由算法控制，不受年龄分段限制

## 使用示例

### 1. 创建多阶段筛查策略

```python
from src.ccsm.modules.screening import ScreeningStrategy, ScreeningToolConfig
from src.ccsm.core.enums import ScreeningTool

# 创建多阶段筛查策略
strategy = ScreeningStrategy(
    name="multi_stage_screening",
    tool_configs=[
        ScreeningToolConfig(
            tool=ScreeningTool.RISK_QUESTIONNAIRE,
            start_age=45,
            end_age=50,
            interval=5.0,
            compliance_rate=0.85
        ),
        ScreeningToolConfig(
            tool=ScreeningTool.FIT,
            start_age=50,
            end_age=65,
            interval=2.0,
            compliance_rate=0.75,
            follow_up_compliance_rate=0.80
        ),
        ScreeningToolConfig(
            tool=ScreeningTool.COLONOSCOPY,
            start_age=65,
            end_age=75,
            interval=10.0,
            compliance_rate=0.65
        )
    ],
    sequential=True
)
```

### 2. 获取详细统计信息

```python
# 执行人群筛查
stats = screening_module.screen_population(population, "annual_fit", 2024)

# 查看详细统计
print(f"初筛次数: {stats['primary_screening_count']}")
print(f"确诊性肠镜次数: {stats['diagnostic_colonoscopy_count']}")
print(f"后续依从率: {stats['follow_up_compliance_rate']*100:.1f}%")
print(f"平均后续依从性概率: {stats['average_follow_up_compliance_rate']*100:.1f}%")

# 按工具查看筛查次数
for tool, count in stats['screening_counts_by_tool'].items():
    if count > 0:
        print(f"{tool}: {count}次")
```

### 3. 数值型依从性配置

```python
# 配置不同依从性水平的策略
high_compliance_strategy = ScreeningStrategy(
    name="high_compliance",
    tool_configs=[
        ScreeningToolConfig(
            tool=ScreeningTool.FIT,
            start_age=50,
            end_age=75,
            interval=1.0,
            compliance_rate=0.80,              # 80%筛查依从性
            follow_up_compliance_rate=0.90     # 90%后续肠镜依从性
        )
    ],
    sequential=False
)

# 查看筛查记录中的依从性信息
for record in individual.screening_history:
    if record.detected:
        print(f"依从性概率: {record.follow_up_compliance_rate}")
        print(f"实际依从: {'是' if record.follow_up_compliant else '否'}")
```

### 3. 向后兼容性

```python
# 旧格式仍然支持
old_strategy = ScreeningStrategy(
    name="old_format",
    start_age=50,
    end_age=75,
    tools=[ScreeningTool.FIT],
    intervals=[1.0],
    sequential=False
)
# 自动转换为新格式
```

## 预定义策略

模型提供了多种预定义策略：

1. **annual_fit** - 年度FIT筛查
2. **biennial_fit** - 双年度FIT筛查
3. **colonoscopy_10y** - 10年结肠镜筛查
4. **fit_colonoscopy** - FIT+结肠镜贯序筛查
5. **sigmoidoscopy_colonoscopy** - 乙状结直肠镜+结肠镜贯序筛查
6. **risk_stratified** - 风险分层筛查
7. **multi_stage_sequential** - 多阶段贯序筛查

## 测试和验证

### 运行测试
```bash
python tests/test_enhanced_screening.py
```

### 运行演示
```bash
python examples/enhanced_screening_demo.py
```

## 技术实现要点

### 1. 数据结构设计
- 使用 `@dataclass` 确保类型安全
- 支持可选参数，提供默认值
- 保持向后兼容性

### 2. 验证机制
- 年龄范围合理性检查
- 工具配置完整性验证
- 贯序筛查逻辑验证

### 3. 统计算法
- 实时统计各类筛查次数
- 自动计算依从率和效果指标
- 支持多维度数据分析

## 性能优化

- 使用高效的数据结构
- 避免重复计算
- 支持大规模人群仿真

## 未来扩展

1. 支持更多筛查工具
2. 增加成本效果分析
3. 支持动态策略调整
4. 集成机器学习预测模型

## 注意事项

1. 确保年龄范围设置合理
2. 依从性参数应基于实际数据
3. 贯序筛查逻辑需要仔细验证
4. 大规模仿真时注意内存使用

## 联系方式

如有问题或建议，请联系开发团队。

"""
统一配置加载器模块
支持从Excel和JSON文件加载所有模型配置数据，包括：
- 筛查工具和策略配置
- 基准数据（发病率、死亡率等）
- 寿命表数据
- 人口构成数据
- 筛查性能数据
"""

import json
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Tuple
from dataclasses import asdict
import warnings

from .screening import (
    ScreeningParameters, ScreeningStrategy, ScreeningToolConfig,
    ScreeningTool
)
from ..core.enums import CancerStage


class UnifiedConfigLoader:
    """统一配置加载器"""

    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置加载器

        Args:
            config_dir: 配置文件目录，默认为项目根目录下的config文件夹
        """
        if config_dir is None:
            # 默认使用项目根目录下的config文件夹
            self.config_dir = Path(__file__).parent.parent.parent.parent / "config"
        else:
            self.config_dir = Path(config_dir)

        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)

        # 筛查配置文件路径
        self.screening_params_file = self.config_dir / "screening_parameters.json"
        self.screening_strategies_file = self.config_dir / "screening_strategies.xlsx"
        self.screening_tools_file = self.config_dir / "screening_tools.xlsx"

        # 数据文件路径（统一到config目录）
        self.benchmark_file = self.config_dir / "default_benchmarks.xlsx"
        self.lifetable_file = self.config_dir / "lifetable.xlsx"
        self.population_file = self.config_dir / "population.xlsx"
        self.screening_data_file = self.config_dir / "screening_data.xlsx"

        # 缓存加载的数据
        self._benchmark_data = None
        self._lifetable_data = None
        self._population_data = None
        self._screening_data = None
    
    def load_screening_parameters_from_json(self, file_path: Optional[str] = None) -> ScreeningParameters:
        """
        从JSON文件加载筛查参数

        Args:
            file_path: JSON文件路径，如果为None则使用默认路径

        Returns:
            筛查参数对象
        """
        if file_path is None:
            file_path = self.screening_params_file
        else:
            # 如果是相对路径，则相对于配置目录
            file_path = Path(file_path)
            if not file_path.is_absolute():
                file_path = self.config_dir / file_path

        if not file_path.exists():
            print(f"❌ 筛查参数文件不存在: {file_path}")
            print("使用默认参数")
            return ScreeningParameters()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 转换字符串键为枚举
            params = self._convert_json_to_screening_parameters(data)
            print(f"✅ 成功从JSON加载筛查参数: {file_path}")
            return params
            
        except Exception as e:
            print(f"❌ 加载筛查参数失败: {e}")
            print("使用默认参数")
            return ScreeningParameters()
    
    def load_screening_strategies_from_excel(self, file_path: Optional[str] = None) -> List[ScreeningStrategy]:
        """
        从Excel文件加载筛查策略

        Args:
            file_path: Excel文件路径，如果为None则使用默认路径

        Returns:
            筛查策略列表
        """
        if file_path is None:
            file_path = self.screening_strategies_file
        else:
            # 如果是相对路径，则相对于配置目录
            file_path = Path(file_path)
            if not file_path.is_absolute():
                file_path = self.config_dir / file_path

        if not file_path.exists():
            print(f"❌ 筛查策略文件不存在: {file_path}")
            return []
        
        try:
            # 读取策略配置表
            strategies_df = pd.read_excel(file_path, sheet_name='strategies')
            # 读取工具配置表
            tools_df = pd.read_excel(file_path, sheet_name='tool_configs')
            
            strategies = self._convert_excel_to_strategies(strategies_df, tools_df)
            print(f"✅ 成功从Excel加载 {len(strategies)} 个筛查策略: {file_path}")
            return strategies
            
        except Exception as e:
            print(f"❌ 加载筛查策略失败: {e}")
            return []
    
    def load_screening_tools_from_excel(self, file_path: Optional[str] = None) -> Dict[str, List[ScreeningToolConfig]]:
        """
        从Excel文件加载筛查工具配置

        Args:
            file_path: Excel文件路径，如果为None则使用默认路径

        Returns:
            按策略名称分组的筛查工具配置字典
        """
        if file_path is None:
            file_path = self.screening_tools_file
        else:
            # 如果是相对路径，则相对于配置目录
            file_path = Path(file_path)
            if not file_path.is_absolute():
                file_path = self.config_dir / file_path

        if not file_path.exists():
            print(f"❌ 筛查工具配置文件不存在: {file_path}")
            return {}
        
        try:
            df = pd.read_excel(file_path)
            tool_configs = self._convert_excel_to_tool_configs(df)
            print(f"✅ 成功从Excel加载筛查工具配置: {file_path}")
            return tool_configs
            
        except Exception as e:
            print(f"❌ 加载筛查工具配置失败: {e}")
            return {}
    
    def save_screening_parameters_to_json(self, params: ScreeningParameters,
                                        file_path: Optional[str] = None) -> None:
        """
        将筛查参数保存到JSON文件

        Args:
            params: 筛查参数对象
            file_path: JSON文件路径，如果为None则使用默认路径
        """
        if file_path is None:
            file_path = self.screening_params_file
        else:
            # 如果是相对路径，则相对于配置目录
            file_path = Path(file_path)
            if not file_path.is_absolute():
                file_path = self.config_dir / file_path
        
        try:
            # 转换为可序列化的字典
            data = self._convert_screening_parameters_to_json(params)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 筛查参数已保存到JSON: {file_path}")
            
        except Exception as e:
            print(f"❌ 保存筛查参数失败: {e}")
    
    def save_screening_strategies_to_excel(self, strategies: List[ScreeningStrategy],
                                         file_path: Optional[str] = None) -> None:
        """
        将筛查策略保存到Excel文件

        Args:
            strategies: 筛查策略列表
            file_path: Excel文件路径，如果为None则使用默认路径
        """
        if file_path is None:
            file_path = self.screening_strategies_file
        else:
            # 如果是相对路径，则相对于配置目录
            file_path = Path(file_path)
            if not file_path.is_absolute():
                file_path = self.config_dir / file_path
        
        try:
            # 转换策略为DataFrame
            strategies_data, tools_data = self._convert_strategies_to_excel(strategies)
            
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                strategies_data.to_excel(writer, sheet_name='strategies', index=False)
                tools_data.to_excel(writer, sheet_name='tool_configs', index=False)
            
            print(f"✅ 筛查策略已保存到Excel: {file_path}")
            
        except Exception as e:
            print(f"❌ 保存筛查策略失败: {e}")
    
    def create_template_files(self) -> None:
        """创建模板配置文件"""
        # 创建默认筛查参数模板
        default_params = ScreeningParameters()
        self.save_screening_parameters_to_json(default_params)
        
        # 创建筛查策略模板
        template_strategies = self._create_template_strategies()
        self.save_screening_strategies_to_excel(template_strategies)
        
        print(f"✅ 模板配置文件已创建在: {self.config_dir}")
    
    def _convert_json_to_screening_parameters(self, data: Dict) -> ScreeningParameters:
        """将JSON数据转换为筛查参数对象"""
        # 转换敏感性数据
        sensitivity = {}
        if 'sensitivity' in data:
            for tool_str, stages_dict in data['sensitivity'].items():
                tool = ScreeningTool(tool_str)
                sensitivity[tool] = {}
                for stage_str, value in stages_dict.items():
                    # 根据字符串名称获取对应的CancerStage枚举
                    stage = self._get_cancer_stage_by_name(stage_str)
                    if stage:
                        sensitivity[tool][stage] = float(value)
        
        # 转换特异性数据
        specificity = {}
        if 'specificity' in data:
            for tool_str, value in data['specificity'].items():
                tool = ScreeningTool(tool_str)
                specificity[tool] = float(value)
        
        # 转换依从性数据
        compliance = {}
        if 'compliance' in data:
            for tool_str, value in data['compliance'].items():
                tool = ScreeningTool(tool_str)
                compliance[tool] = float(value)
        
        # 转换随访依从性数据
        follow_up_compliance = {}
        if 'follow_up_compliance' in data:
            for tool_str, value in data['follow_up_compliance'].items():
                tool = ScreeningTool(tool_str)
                follow_up_compliance[tool] = float(value)
        
        # 转换成本数据
        costs = {}
        if 'costs' in data:
            for tool_str, value in data['costs'].items():
                tool = ScreeningTool(tool_str)
                costs[tool] = float(value)
        
        return ScreeningParameters(
            sensitivity=sensitivity or None,
            specificity=specificity or None,
            compliance=compliance or None,
            follow_up_compliance=follow_up_compliance or None,
            costs=costs or None
        )
    
    def _convert_screening_parameters_to_json(self, params: ScreeningParameters) -> Dict:
        """将筛查参数对象转换为JSON可序列化的字典"""
        data = {}
        
        # 转换敏感性数据
        if params.sensitivity:
            data['sensitivity'] = {}
            for tool, stages_dict in params.sensitivity.items():
                data['sensitivity'][tool.value] = {}
                for stage, value in stages_dict.items():
                    data['sensitivity'][tool.value][stage.value] = value
        
        # 转换特异性数据
        if params.specificity:
            data['specificity'] = {tool.value: value for tool, value in params.specificity.items()}
        
        # 转换依从性数据
        if params.compliance:
            data['compliance'] = {tool.value: value for tool, value in params.compliance.items()}
        
        # 转换随访依从性数据
        if params.follow_up_compliance:
            data['follow_up_compliance'] = {tool.value: value for tool, value in params.follow_up_compliance.items()}
        
        # 转换成本数据
        if params.costs:
            data['costs'] = {tool.value: value for tool, value in params.costs.items()}
        
        return data

    def _get_cancer_stage_by_name(self, stage_name: str) -> Optional[CancerStage]:
        """根据名称获取CancerStage枚举"""
        stage_mapping = {
            'NORMAL': CancerStage.NORMAL,
            'LOW_RISK_ADENOMA': CancerStage.LOW_RISK_ADENOMA,
            'HIGH_RISK_ADENOMA': CancerStage.HIGH_RISK_ADENOMA,
            'SMALL_SERRATED_ADENOMA': CancerStage.SMALL_SERRATED_ADENOMA,
            'LARGE_SERRATED_ADENOMA': CancerStage.LARGE_SERRATED_ADENOMA,
            'PRECLINICAL_CANCER': CancerStage.PRECLINICAL_CANCER,
            'CLINICAL_CANCER_STAGE_I': CancerStage.CLINICAL_CANCER_STAGE_I,
            'CLINICAL_CANCER_STAGE_II': CancerStage.CLINICAL_CANCER_STAGE_II,
            'CLINICAL_CANCER_STAGE_III': CancerStage.CLINICAL_CANCER_STAGE_III,
            'CLINICAL_CANCER_STAGE_IV': CancerStage.CLINICAL_CANCER_STAGE_IV
        }
        return stage_mapping.get(stage_name.upper())

    def _get_screening_tool_by_name(self, tool_name: str) -> Optional[ScreeningTool]:
        """根据名称获取ScreeningTool枚举"""
        tool_mapping = {
            'FIT': ScreeningTool.FIT,
            'COLONOSCOPY': ScreeningTool.COLONOSCOPY,
            'SIGMOIDOSCOPY': ScreeningTool.SIGMOIDOSCOPY,
            'RISK_QUESTIONNAIRE': ScreeningTool.RISK_QUESTIONNAIRE,
            'RISKQUESTIONNAIRE': ScreeningTool.RISK_QUESTIONNAIRE,  # 兼容性
            'OTHER': ScreeningTool.OTHER
        }
        return tool_mapping.get(tool_name.upper())

    def _convert_excel_to_strategies(self, strategies_df: pd.DataFrame,
                                   tools_df: pd.DataFrame) -> List[ScreeningStrategy]:
        """将Excel数据转换为筛查策略列表"""
        strategies = []

        for _, row in strategies_df.iterrows():
            strategy_name = row['strategy_name']

            # 获取该策略的工具配置
            strategy_tools = tools_df[tools_df['strategy_name'] == strategy_name]
            tool_configs = []

            for _, tool_row in strategy_tools.iterrows():
                # 使用辅助方法获取工具枚举
                tool = self._get_screening_tool_by_name(str(tool_row['tool']))
                if not tool:
                    print(f"⚠️ 未知的筛查工具: {tool_row['tool']}")
                    continue

                tool_config = ScreeningToolConfig(
                    tool=tool,
                    start_age=int(tool_row['start_age']),
                    end_age=int(tool_row['end_age']),
                    interval=float(tool_row['interval']),
                    compliance_rate=float(tool_row['compliance_rate']) if pd.notna(tool_row['compliance_rate']) else None,
                    follow_up_compliance_rate=float(tool_row['follow_up_compliance_rate']) if pd.notna(tool_row['follow_up_compliance_rate']) else None
                )
                tool_configs.append(tool_config)

            strategy = ScreeningStrategy(
                name=strategy_name,
                tool_configs=tool_configs,
                sequential=bool(row['sequential']) if pd.notna(row['sequential']) else True,
                risk_stratified=bool(row['risk_stratified']) if pd.notna(row['risk_stratified']) else False,
                high_risk_interval=float(row['high_risk_interval']) if pd.notna(row['high_risk_interval']) else None
            )
            strategies.append(strategy)

        return strategies

    def _convert_excel_to_tool_configs(self, df: pd.DataFrame) -> Dict[str, List[ScreeningToolConfig]]:
        """将Excel数据转换为工具配置字典"""
        tool_configs = {}

        for _, row in df.iterrows():
            strategy_name = row['strategy_name']

            if strategy_name not in tool_configs:
                tool_configs[strategy_name] = []

            # 使用辅助方法获取工具枚举
            tool = self._get_screening_tool_by_name(str(row['tool']))
            if not tool:
                print(f"⚠️ 未知的筛查工具: {row['tool']}")
                continue

            tool_config = ScreeningToolConfig(
                tool=tool,
                start_age=int(row['start_age']),
                end_age=int(row['end_age']),
                interval=float(row['interval']),
                compliance_rate=float(row['compliance_rate']) if pd.notna(row['compliance_rate']) else None,
                follow_up_compliance_rate=float(row['follow_up_compliance_rate']) if pd.notna(row['follow_up_compliance_rate']) else None
            )
            tool_configs[strategy_name].append(tool_config)

        return tool_configs

    def _convert_strategies_to_excel(self, strategies: List[ScreeningStrategy]) -> tuple[pd.DataFrame, pd.DataFrame]:
        """将筛查策略转换为Excel格式的DataFrame"""
        strategies_data = []
        tools_data = []

        for strategy in strategies:
            # 策略基本信息
            strategy_row = {
                'strategy_name': strategy.name,
                'sequential': strategy.sequential,
                'risk_stratified': strategy.risk_stratified,
                'high_risk_interval': strategy.high_risk_interval
            }
            strategies_data.append(strategy_row)

            # 工具配置信息
            if strategy.tool_configs:
                for tool_config in strategy.tool_configs:
                    tool_row = {
                        'strategy_name': strategy.name,
                        'tool': tool_config.tool.value,
                        'start_age': tool_config.start_age,
                        'end_age': tool_config.end_age,
                        'interval': tool_config.interval,
                        'compliance_rate': tool_config.compliance_rate,
                        'follow_up_compliance_rate': tool_config.follow_up_compliance_rate
                    }
                    tools_data.append(tool_row)

        return pd.DataFrame(strategies_data), pd.DataFrame(tools_data)

    def _create_template_strategies(self) -> List[ScreeningStrategy]:
        """创建模板筛查策略"""
        strategies = []

        # 年度FIT策略
        annual_fit = ScreeningStrategy(
            name="annual_fit_template",
            tool_configs=[
                ScreeningToolConfig(
                    tool=ScreeningTool.FIT,
                    start_age=50,
                    end_age=75,
                    interval=1.0,
                    compliance_rate=0.70,
                    follow_up_compliance_rate=0.75
                )
            ],
            sequential=False,
            risk_stratified=False
        )
        strategies.append(annual_fit)

        # 风险分层策略
        risk_stratified = ScreeningStrategy(
            name="risk_stratified_template",
            tool_configs=[
                ScreeningToolConfig(
                    tool=ScreeningTool.RISK_QUESTIONNAIRE,
                    start_age=45,
                    end_age=50,
                    interval=5.0,
                    compliance_rate=0.80
                ),
                ScreeningToolConfig(
                    tool=ScreeningTool.FIT,
                    start_age=50,
                    end_age=65,
                    interval=1.0,
                    compliance_rate=0.70,
                    follow_up_compliance_rate=0.75
                ),
                ScreeningToolConfig(
                    tool=ScreeningTool.COLONOSCOPY,
                    start_age=65,
                    end_age=75,
                    interval=10.0,
                    compliance_rate=0.60
                )
            ],
            sequential=True,
            risk_stratified=True,
            high_risk_interval=1.0
        )
        strategies.append(risk_stratified)

        return strategies

    # ==================== 数据加载方法 ====================

    def load_benchmark_data(self) -> Dict[str, Dict]:
        """
        加载校准基准值数据

        Returns:
            包含各种基准指标的字典
        """
        if self._benchmark_data is not None:
            return self._benchmark_data

        file_path = self.benchmark_file

        try:
            # 读取Excel文件的所有工作表
            excel_file = pd.ExcelFile(file_path)
            benchmark_data = {}

            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                benchmark_data[sheet_name] = df

            # 处理常见的基准指标
            processed_data = self._process_benchmark_data(benchmark_data)

            self._benchmark_data = processed_data
            print(f"✅ 成功加载基准数据，包含 {len(processed_data)} 个指标组")

            return processed_data

        except FileNotFoundError:
            print(f"❌ 未找到基准数据文件: {file_path}")
            return self._get_default_benchmark_data()
        except Exception as e:
            print(f"❌ 加载基准数据失败: {e}")
            return self._get_default_benchmark_data()

    def load_lifetable_data(self) -> Dict[str, Dict[int, float]]:
        """
        加载寿命表数据

        Returns:
            按性别和年龄分组的死亡率数据
        """
        if self._lifetable_data is not None:
            return self._lifetable_data

        file_path = self.lifetable_file

        try:
            # 读取寿命表数据
            df = pd.read_excel(file_path)

            # 处理寿命表数据
            lifetable_data = self._process_lifetable_data(df)

            self._lifetable_data = lifetable_data
            print(f"✅ 成功加载寿命表数据")

            return lifetable_data

        except FileNotFoundError:
            print(f"❌ 未找到寿命表文件: {file_path}")
            return self._get_default_lifetable_data()
        except Exception as e:
            print(f"❌ 加载寿命表数据失败: {e}")
            return self._get_default_lifetable_data()

    def load_population_data(self) -> Dict[str, any]:
        """
        加载人口构成数据

        Returns:
            包含年龄分布和性别比例的数据
        """
        if self._population_data is not None:
            return self._population_data

        file_path = self.population_file

        try:
            # 读取人口构成数据
            df = pd.read_excel(file_path)

            # 处理人口数据
            population_data = self._process_population_data(df)

            self._population_data = population_data
            print(f"✅ 成功加载南山区人口构成数据")

            return population_data

        except FileNotFoundError:
            print(f"❌ 未找到人口构成文件: {file_path}")
            return self._get_default_population_data()
        except Exception as e:
            print(f"❌ 加载人口构成数据失败: {e}")
            return self._get_default_population_data()

    def load_screening_data(self) -> Dict[str, Dict]:
        """
        加载筛查性能数据

        Returns:
            包含筛查性能的数据
        """
        if self._screening_data is not None:
            return self._screening_data

        file_path = self.screening_data_file

        try:
            # 读取筛查数据
            df = pd.read_excel(file_path)

            # 处理筛查数据
            screening_data = self._extract_screening_data(df)

            self._screening_data = screening_data
            print(f"✅ 成功加载筛查数据")

            return screening_data

        except FileNotFoundError:
            print(f"❌ 未找到筛查数据文件: {file_path}")
            return {}
        except Exception as e:
            print(f"❌ 加载筛查数据失败: {e}")
            return {}

    def load_custom_lifetable(self, file_path: str) -> Dict[str, Dict[int, float]]:
        """
        从自定义文件加载生命表数据

        Args:
            file_path: 生命表文件路径，支持CSV和Excel格式

        Returns:
            按性别和年龄分组的死亡率数据
        """
        try:
            # 如果是相对路径，则相对于配置目录
            file_path = Path(file_path)
            if not file_path.is_absolute():
                file_path = self.config_dir / file_path

            if not file_path.exists():
                raise FileNotFoundError(f"生命表文件不存在: {file_path}")

            # 根据文件扩展名选择读取方式
            if file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            elif file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}，仅支持 .xlsx, .xls, .csv")

            # 验证数据格式
            if df.empty:
                raise ValueError("生命表文件为空")

            # 处理生命表数据
            lifetable_data = self._process_lifetable_data(df)

            # 验证处理结果
            if not any(lifetable_data.values()):
                raise ValueError("未能从文件中提取有效的生命表数据，请检查文件格式")

            print(f"✅ 成功加载自定义生命表: {file_path}")
            print(f"   - 男性数据: {len(lifetable_data.get('male', {}))} 个年龄组")
            print(f"   - 女性数据: {len(lifetable_data.get('female', {}))} 个年龄组")
            print(f"   - 总体数据: {len(lifetable_data.get('total', {}))} 个年龄组")

            return lifetable_data

        except Exception as e:
            print(f"❌ 加载自定义生命表失败: {e}")
            print("💡 请确保文件格式正确，包含年龄列和死亡率列")
            print("💡 支持的列名示例:")
            print("   - 年龄列: 'age', '年龄', 'Age', 'x'")
            print("   - 男性死亡率: 'male', '男', 'qx_m', 'mx_m'")
            print("   - 女性死亡率: 'female', '女', 'qx_f', 'mx_f'")
            print("   - 总体死亡率: 'total', 'qx', 'mx', '死亡率'")
            return self._get_default_lifetable_data()

    # ==================== 数据处理方法 ====================

    def _process_benchmark_data(self, raw_data: Dict[str, pd.DataFrame]) -> Dict[str, Dict]:
        """处理基准数据"""
        processed = {}

        for sheet_name, df in raw_data.items():
            try:
                if 'incidence' in sheet_name.lower() or '发病' in sheet_name:
                    # 处理发病率数据
                    processed['incidence_rates'] = self._extract_age_gender_rates(df)

                elif 'mortality' in sheet_name.lower() or '死亡' in sheet_name:
                    # 处理死亡率数据
                    processed['mortality_rates'] = self._extract_age_gender_rates(df)

                elif 'low_risk_adenoma' in sheet_name.lower() or '腺瘤' in sheet_name:
                    # 处理低风险腺瘤患病率数据
                    processed['low_risk_adenoma_prevalence'] = self._extract_age_gender_rates(df)

                elif 'high_risk_adenoma' in sheet_name.lower() or '腺瘤' in sheet_name:
                    # 处理高风险腺瘤患病率数据
                    processed['high_risk_adenoma_prevalence'] = self._extract_age_gender_rates(df)

                else:
                    # 其他数据直接保存
                    processed[sheet_name] = df.to_dict('records')

            except Exception as e:
                print(f"⚠️ 处理工作表 {sheet_name} 时出错: {e}")
                continue

        return processed

    def _extract_age_gender_rates(self, df: pd.DataFrame) -> Dict[str, Dict[int, float]]:
        """从DataFrame中提取按年龄和性别分组的比率数据"""
        rates = {'male': {}, 'female': {}, 'total': {}}

        # 检查数据格式：基准数据是按行存储的，格式为 [metric, gender, age, value]
        if 'gender' in df.columns and 'age' in df.columns and 'value' in df.columns:
            # 按行处理数据
            for _, row in df.iterrows():
                try:
                    gender = str(row['gender']).lower()
                    age = int(row['age'])
                    value = float(row['value'])

                    # 转换为每10万人的比率（如果值大于1，假设是每10万人）
                    if value > 1:
                        value = value / 100000.0

                    if gender in ['male', '男', '男性']:
                        rates['male'][age] = value
                    elif gender in ['female', '女', '女性']:
                        rates['female'][age] = value
                    elif gender in ['total', '合计', '总计']:
                        rates['total'][age] = value

                except (ValueError, TypeError, KeyError):
                    continue
        else:
            # 原有的按列处理逻辑（作为备用）
            # 尝试识别年龄列
            age_col = None
            for col in df.columns:
                if any(keyword in str(col).lower() for keyword in ['age', '年龄', 'Age']):
                    age_col = col
                    break

            if age_col is None and len(df.columns) > 0:
                age_col = df.columns[0]  # 使用第一列作为年龄列

            # 尝试识别性别相关列
            for col in df.columns:
                col_name = str(col).lower()
                if any(keyword in col_name for keyword in ['male', '男', '男性']):
                    for _, row in df.iterrows():
                        try:
                            age = int(row[age_col])
                            rate = float(row[col])
                            rates['male'][age] = rate
                        except (ValueError, TypeError):
                            continue

                elif any(keyword in col_name for keyword in ['female', '女', '女性']):
                    for _, row in df.iterrows():
                        try:
                            age = int(row[age_col])
                            rate = float(row[col])
                            rates['female'][age] = rate
                        except (ValueError, TypeError):
                            continue

                elif any(keyword in col_name for keyword in ['total', '合计', '总计', 'rate', '率']):
                    for _, row in df.iterrows():
                        try:
                            age = int(row[age_col])
                            rate = float(row[col])
                            rates['total'][age] = rate
                        except (ValueError, TypeError):
                            continue

        return rates

    def _extract_screening_data(self, df: pd.DataFrame) -> Dict[str, float]:
        """提取筛查性能数据"""
        screening_data = {}

        for _, row in df.iterrows():
            try:
                # 尝试提取筛查工具和性能指标
                tool_name = str(row.iloc[0]).lower()

                if 'fit' in tool_name:
                    screening_data['fit_sensitivity'] = float(row.iloc[1]) if len(row) > 1 else 0.8
                    screening_data['fit_specificity'] = float(row.iloc[2]) if len(row) > 2 else 0.95

                elif 'colonoscopy' in tool_name or '结肠镜' in tool_name:
                    screening_data['colonoscopy_sensitivity'] = float(row.iloc[1]) if len(row) > 1 else 0.95
                    screening_data['colonoscopy_specificity'] = float(row.iloc[2]) if len(row) > 2 else 0.99

            except (ValueError, TypeError):
                continue

        return screening_data

    def _process_lifetable_data(self, df: pd.DataFrame) -> Dict[str, Dict[int, float]]:
        """处理寿命表数据"""
        lifetable = {'male': {}, 'female': {}, 'total': {}}

        # 尝试识别列名
        age_col = None
        for col in df.columns:
            if any(keyword in str(col).lower() for keyword in ['age', '年龄', 'x']):
                age_col = col
                break

        if age_col is None:
            age_col = df.columns[0]

        # 提取死亡率数据
        for col in df.columns:
            col_name = str(col).lower()

            if any(keyword in col_name for keyword in ['male', '男', 'qx_m', 'mx_m']):
                for _, row in df.iterrows():
                    try:
                        age = int(row[age_col])
                        mortality_rate = float(row[col])
                        lifetable['male'][age] = mortality_rate
                    except (ValueError, TypeError):
                        continue

            elif any(keyword in col_name for keyword in ['female', '女', 'qx_f', 'mx_f']):
                for _, row in df.iterrows():
                    try:
                        age = int(row[age_col])
                        mortality_rate = float(row[col])
                        lifetable['female'][age] = mortality_rate
                    except (ValueError, TypeError):
                        continue

            elif any(keyword in col_name for keyword in ['total', 'qx', 'mx', '死亡率']):
                for _, row in df.iterrows():
                    try:
                        age = int(row[age_col])
                        mortality_rate = float(row[col])
                        lifetable['total'][age] = mortality_rate
                    except (ValueError, TypeError):
                        continue

        return lifetable

    def _process_population_data(self, df: pd.DataFrame) -> Dict[str, any]:
        """处理人口构成数据"""
        population_data = {
            'age_distribution': {},
            'gender_ratio': 0.5,
            'total_population': 0
        }

        # 提取人口数据
        total_pop = 0
        male_pop = 0
        female_pop = 0

        # 处理每一行数据
        for _, row in df.iterrows():
            try:
                # 解析年龄组
                age_group = str(row['age_groups'])
                male_count = float(row['male'])
                female_count = float(row['female'])
                total_count = float(row['total'])

                # 累计性别人口
                male_pop += male_count
                female_pop += female_count
                total_pop += total_count

                # 处理年龄组，转换为中位年龄
                if '-' in age_group:
                    # 处理年龄段，如"65-69"
                    age_parts = age_group.split('-')
                    if len(age_parts) == 2:
                        try:
                            start_age = int(age_parts[0])
                            end_age = int(age_parts[1])
                            mid_age = (start_age + end_age) // 2
                        except ValueError:
                            continue
                    else:
                        continue
                elif '+' in age_group:
                    # 处理开放年龄组，如"85+"
                    try:
                        mid_age = int(age_group.replace('+', ''))
                    except ValueError:
                        continue
                else:
                    # 处理单一年龄
                    try:
                        mid_age = int(age_group)
                    except ValueError:
                        continue

                # 存储年龄分布（使用总人口）
                population_data['age_distribution'][mid_age] = total_count

            except (ValueError, TypeError, KeyError) as e:
                print(f"⚠️ 跳过无效行: {e}")
                continue

        # 计算男性比例
        if male_pop + female_pop > 0:
            population_data['gender_ratio'] = male_pop / (male_pop + female_pop)
            print(f"📊 计算得出性别比例 - 男性: {male_pop:,}, 女性: {female_pop:,}")
            print(f"📊 男性比例: {population_data['gender_ratio']:.3f}")

        # 标准化年龄分布为比例
        if total_pop > 0:
            for age in population_data['age_distribution']:
                population_data['age_distribution'][age] /= total_pop

            # 验证年龄分布总和
            age_sum = sum(population_data['age_distribution'].values())
            print(f"📊 年龄分布比例总和: {age_sum:.6f}")

            # 如果总和不为1，进行标准化
            if abs(age_sum - 1.0) > 0.001:
                print(f"⚠️ 年龄分布总和不为1，进行标准化")
                for age in population_data['age_distribution']:
                    population_data['age_distribution'][age] /= age_sum

        population_data['total_population'] = total_pop
        print(f"📊 总人口: {total_pop:,}")

        return population_data

    # ==================== 默认数据方法 ====================

    def _get_default_benchmark_data(self) -> Dict[str, Dict]:
        """获取默认基准数据"""
        return {
            'incidence_rates': {
                'male': {50: 0.0001, 55: 0.0002, 60: 0.0004, 65: 0.0008, 70: 0.0015},
                'female': {50: 0.00008, 55: 0.00015, 60: 0.0003, 65: 0.0006, 70: 0.0012},
                'total': {50: 0.00009, 55: 0.000175, 60: 0.00035, 65: 0.0007, 70: 0.00135}
            },
            'mortality_rates': {
                'male': {50: 0.00005, 55: 0.0001, 60: 0.0002, 65: 0.0004, 70: 0.0008},
                'female': {50: 0.00004, 55: 0.00008, 60: 0.00015, 65: 0.0003, 70: 0.0006},
                'total': {50: 0.000045, 55: 0.00009, 60: 0.000175, 65: 0.00035, 70: 0.0007}
            },
            'adenoma_prevalence': {
                'male': {50: 0.15, 55: 0.20, 60: 0.25, 65: 0.30, 70: 0.35},
                'female': {50: 0.10, 55: 0.15, 60: 0.20, 65: 0.25, 70: 0.30},
                'total': {50: 0.125, 55: 0.175, 60: 0.225, 65: 0.275, 70: 0.325}
            }
        }

    def _get_default_lifetable_data(self) -> Dict[str, Dict[int, float]]:
        """获取默认寿命表数据"""
        # 基于中国人口寿命表的简化数据
        ages = list(range(0, 101))
        male_rates = {}
        female_rates = {}

        for age in ages:
            if age < 1:
                male_rates[age] = 0.007
                female_rates[age] = 0.006
            elif age < 5:
                male_rates[age] = 0.0005
                female_rates[age] = 0.0004
            elif age < 15:
                male_rates[age] = 0.0002
                female_rates[age] = 0.0001
            elif age < 25:
                male_rates[age] = 0.0008
                female_rates[age] = 0.0003
            elif age < 35:
                male_rates[age] = 0.001
                female_rates[age] = 0.0005
            elif age < 45:
                male_rates[age] = 0.002
                female_rates[age] = 0.001
            elif age < 55:
                male_rates[age] = 0.005
                female_rates[age] = 0.003
            elif age < 65:
                male_rates[age] = 0.01
                female_rates[age] = 0.007
            elif age < 75:
                male_rates[age] = 0.025
                female_rates[age] = 0.018
            elif age < 85:
                male_rates[age] = 0.06
                female_rates[age] = 0.045
            else:
                male_rates[age] = 0.15
                female_rates[age] = 0.12

        return {
            'male': male_rates,
            'female': female_rates,
            'total': {age: (male_rates[age] + female_rates[age]) / 2 for age in ages}
        }

    def _get_default_population_data(self) -> Dict[str, any]:
        """获取默认人口构成数据"""
        return {
            'age_distribution': {
                40: 0.05, 45: 0.08, 50: 0.12, 55: 0.15, 60: 0.18,
                65: 0.20, 70: 0.15, 75: 0.07
            },
            'gender_ratio': 0.52,
            'total_population': 1000000
        }


class UnifiedConfigManager:
    """统一配置管理器"""

    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置管理器

        Args:
            config_dir: 配置文件目录
        """
        self.loader = UnifiedConfigLoader(config_dir)
        self.current_parameters: Optional[ScreeningParameters] = None
        self.current_strategies: List[ScreeningStrategy] = []

    def initialize_from_config(self,
                             params_file: Optional[str] = None,
                             strategies_file: Optional[str] = None) -> tuple[ScreeningParameters, List[ScreeningStrategy]]:
        """
        从配置文件初始化筛查参数和策略

        Args:
            params_file: 参数文件路径
            strategies_file: 策略文件路径

        Returns:
            (筛查参数, 筛查策略列表)
        """
        # 加载筛查参数
        self.current_parameters = self.loader.load_screening_parameters_from_json(params_file)

        # 加载筛查策略
        self.current_strategies = self.loader.load_screening_strategies_from_excel(strategies_file)

        return self.current_parameters, self.current_strategies

    def get_strategy_by_name(self, name: str) -> Optional[ScreeningStrategy]:
        """根据名称获取筛查策略"""
        for strategy in self.current_strategies:
            if strategy.name == name:
                return strategy
        return None

    def add_strategy(self, strategy: ScreeningStrategy) -> None:
        """添加新的筛查策略"""
        # 检查是否已存在同名策略
        existing_strategy = self.get_strategy_by_name(strategy.name)
        if existing_strategy:
            # 替换现有策略
            index = self.current_strategies.index(existing_strategy)
            self.current_strategies[index] = strategy
            print(f"已更新筛查策略: {strategy.name}")
        else:
            # 添加新策略
            self.current_strategies.append(strategy)
            print(f"已添加筛查策略: {strategy.name}")

    def remove_strategy(self, name: str) -> bool:
        """移除筛查策略"""
        strategy = self.get_strategy_by_name(name)
        if strategy:
            self.current_strategies.remove(strategy)
            print(f"已移除筛查策略: {name}")
            return True
        else:
            print(f"未找到筛查策略: {name}")
            return False

    def list_strategies(self) -> List[str]:
        """列出所有策略名称"""
        return [strategy.name for strategy in self.current_strategies]

    def save_current_config(self,
                          params_file: Optional[str] = None,
                          strategies_file: Optional[str] = None) -> None:
        """保存当前配置"""
        if self.current_parameters:
            self.loader.save_screening_parameters_to_json(self.current_parameters, params_file)

        if self.current_strategies:
            self.loader.save_screening_strategies_to_excel(self.current_strategies, strategies_file)

    def create_config_templates(self) -> None:
        """创建配置模板文件"""
        self.loader.create_template_files()
        print("配置模板文件已创建，您可以根据需要修改这些文件")

    # ==================== 数据加载便利方法 ====================

    def load_benchmark_data(self) -> Dict[str, Dict]:
        """加载基准数据"""
        return self.loader.load_benchmark_data()

    def load_lifetable_data(self) -> Dict[str, Dict[int, float]]:
        """加载寿命表数据"""
        return self.loader.load_lifetable_data()

    def load_population_data(self) -> Dict[str, any]:
        """加载人口构成数据"""
        return self.loader.load_population_data()

    def load_screening_data(self) -> Dict[str, Dict]:
        """加载筛查性能数据"""
        return self.loader.load_screening_data()

    def load_custom_lifetable(self, file_path: str) -> Dict[str, Dict[int, float]]:
        """加载自定义生命表数据"""
        return self.loader.load_custom_lifetable(file_path)

    def load_all_data(self) -> Dict[str, Any]:
        """一次性加载所有数据"""
        return {
            'benchmark_data': self.load_benchmark_data(),
            'lifetable_data': self.load_lifetable_data(),
            'population_data': self.load_population_data(),
            'screening_data': self.load_screening_data(),
            'screening_parameters': self.current_parameters,
            'screening_strategies': self.current_strategies
        }


# 为了向后兼容，保留旧的类名作为别名
ScreeningConfigLoader = UnifiedConfigLoader
ScreeningConfigManager = UnifiedConfigManager

# 筛查配置文件使用指南

## 概述

本指南介绍如何使用Excel和JSON格式的配置文件来定义筛查工具参数和筛查策略，以便在模型初始化时自动加载。

## 配置文件结构

### 1. 筛查参数配置 (JSON格式)

文件路径: `config/screening_parameters.json`

包含以下配置项：

- **sensitivity**: 各筛查工具对不同癌症阶段的敏感性
- **specificity**: 各筛查工具的特异性
- **compliance**: 各筛查工具的依从性
- **follow_up_compliance**: 筛查阳性后接受肠镜检查的依从性
- **costs**: 各筛查工具的成本

#### 支持的筛查工具类型：
- `FIT`: 粪便免疫化学检测
- `COLONOSCOPY`: 结肠镜检查
- `SIGMOIDOSCOPY`: 乙状结肠镜检查
- `RISK_QUESTIONNAIRE`: 风险问卷

#### 支持的癌症阶段：
- `LOW_RISK_ADENOMA`: 低风险腺瘤
- `HIGH_RISK_ADENOMA`: 高风险腺瘤
- `SMALL_SERRATED_ADENOMA`: 小锯齿状腺瘤
- `LARGE_SERRATED_ADENOMA`: 大锯齿状腺瘤
- `PRECLINICAL_CANCER`: 临床前癌症
- `CLINICAL_CANCER_STAGE_I`: 临床癌症I期
- `CLINICAL_CANCER_STAGE_II`: 临床癌症II期
- `CLINICAL_CANCER_STAGE_III`: 临床癌症III期
- `CLINICAL_CANCER_STAGE_IV`: 临床癌症IV期

### 2. 筛查策略配置 (Excel格式)

文件路径: `config/screening_strategies.xlsx`

包含两个工作表：

#### strategies工作表
- `strategy_name`: 策略名称
- `sequential`: 是否为贯序筛查
- `risk_stratified`: 是否基于风险分层
- `high_risk_interval`: 高风险人群筛查间隔

#### tool_configs工作表
- `strategy_name`: 对应的策略名称
- `tool`: 筛查工具类型
- `start_age`: 开始年龄
- `end_age`: 结束年龄
- `interval`: 筛查间隔（年）
- `compliance_rate`: 筛查依从性（可选）
- `follow_up_compliance_rate`: 阳性后肠镜依从性（可选）

## 使用方法

### 1. 基本使用

```python
from src.ccsm.core.model import ColorectalCancerMicrosimulationModel, ModelConfiguration

# 创建配置，启用从文件加载
config = ModelConfiguration(
    initial_population=10000,
    load_config_from_files=True,
    config_dir="config",  # 配置文件目录
    screening_params_file="screening_parameters.json",
    screening_strategies_file="screening_strategies.xlsx"
)

# 初始化模型
model = ColorectalCancerMicrosimulationModel(config=config)

# 查看加载的策略
print("可用策略:", model.list_available_strategies())
```

### 2. 运行时加载配置

```python
# 初始化模型（不从文件加载）
model = ColorectalCancerMicrosimulationModel(initial_population=10000)

# 运行时加载配置
model.load_screening_config_from_files(
    params_file="config/screening_parameters.json",
    strategies_file="config/screening_strategies.xlsx"
)
```

### 3. 保存当前配置

```python
# 保存当前模型的筛查配置到文件
model.save_screening_config_to_files(
    params_file="config/my_screening_parameters.json",
    strategies_file="config/my_screening_strategies.xlsx"
)
```

### 4. 创建配置模板

```python
# 创建默认配置模板文件
model.create_screening_config_templates()
```

## 配置示例

### 示例1: 自定义年度FIT筛查策略

在Excel文件中添加：

**strategies工作表:**
```
strategy_name: my_annual_fit
sequential: False
risk_stratified: False
high_risk_interval: (空)
```

**tool_configs工作表:**
```
strategy_name: my_annual_fit
tool: FIT
start_age: 45
end_age: 80
interval: 1.0
compliance_rate: 0.75
follow_up_compliance_rate: 0.80
```

### 示例2: 多阶段贯序筛查策略

**strategies工作表:**
```
strategy_name: multi_stage_screening
sequential: True
risk_stratified: True
high_risk_interval: 0.5
```

**tool_configs工作表:**
```
strategy_name: multi_stage_screening
tool: RISK_QUESTIONNAIRE
start_age: 40
end_age: 50
interval: 5.0
compliance_rate: 0.85
follow_up_compliance_rate: (空)

strategy_name: multi_stage_screening
tool: FIT
start_age: 50
end_age: 65
interval: 1.0
compliance_rate: 0.70
follow_up_compliance_rate: 0.75

strategy_name: multi_stage_screening
tool: COLONOSCOPY
start_age: 65
end_age: 75
interval: 10.0
compliance_rate: 0.60
follow_up_compliance_rate: (空)
```

## 注意事项

1. **文件格式**: 确保JSON文件格式正确，Excel文件包含必需的工作表
2. **枚举值**: 工具类型和癌症阶段必须使用预定义的枚举值
3. **数据类型**: 年龄和间隔为数值类型，依从性为0-1之间的小数
4. **策略名称**: 每个策略必须有唯一的名称
5. **工具配置**: 每个策略至少需要一个工具配置

## 错误处理

如果配置文件加载失败，模型会：
1. 显示错误信息
2. 使用默认参数继续运行
3. 创建预定义的筛查策略

建议在使用自定义配置前先创建模板文件，然后根据需要修改。

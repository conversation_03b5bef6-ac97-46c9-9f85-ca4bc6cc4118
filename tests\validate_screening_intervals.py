"""
验证筛查间隔修复效果
确保不同间隔产生合理的成本和效果差异
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.ccsm.core.model import ColorectalCancerMicrosimulationModel
import pandas as pd


def validate_screening_intervals():
    """验证筛查间隔修复效果"""
    print("=== 验证筛查间隔修复效果 ===\n")

    # 1. 创建模型实例
    print("1. 创建模型实例...")
    model = ColorectalCancerMicrosimulationModel(initial_population=2000)

    # 2. 设置人口分布
    print("2. 设置人口分布...")
    age_distribution = {
        50: 0.3,  # 50岁：30%
        55: 0.3,  # 55岁：30%
        60: 0.4   # 60岁：40%
    }
    gender_ratio = 0.5  # 男女比例1:1

    # 3. 定义要测试的策略
    strategies_to_test = [
        "fit_6_months",    # 6个月间隔（最频繁）
        "annual_fit",      # 1年间隔
        "fit_1_5_years",   # 1.5年间隔
        "biennial_fit"     # 2年间隔（最少）
    ]

    print(f"3. 测试策略: {strategies_to_test}")

    # 4. 运行各个策略并收集结果
    print("4. 运行各个策略...")
    results_summary = []
    
    for i, strategy_name in enumerate(strategies_to_test):
        print(f"\n运行策略 {i+1}/{len(strategies_to_test)}: {strategy_name}")
        
        # 重新设置人口以确保每个策略都从相同的初始状态开始
        model.setup_population(age_distribution, gender_ratio)
        
        try:
            # 运行策略
            result = model.run_simulation(
                years=15,  # 增加到15年以观察更多差异
                screening_strategy=strategy_name,
                show_progress=False
            )
            
            # 提取关键指标
            economics = result.get('economics_outcome', {})
            final_disease_stats = result.get('final_disease_stats', {})
            screening_summary = result.get('screening_summary', {})
            
            # 计算筛查频率指标
            total_screenings = screening_summary.get('total_screened', 0)
            total_eligible = screening_summary.get('total_eligible', 1)
            screening_rate = total_screenings / total_eligible if total_eligible > 0 else 0
            
            summary = {
                'Strategy': strategy_name,
                'Total_Cost': economics.get('total_cost', 0),
                'Screening_Cost': economics.get('total_screening_cost', 0),
                'Treatment_Cost': economics.get('total_treatment_cost', 0),
                'Cancer_Cases_Prevented': economics.get('cancer_cases_prevented', 0),
                'QALYs_Gained': economics.get('qalys_gained', 0),
                'Cost_per_QALY': economics.get('cost_per_qaly_gained', 0),
                'Total_Screenings': total_screenings,
                'Screening_Rate': screening_rate,
                'Cancer_Incidence': final_disease_stats.get('cancer_incidence', 0),
                'Cancer_Deaths': final_disease_stats.get('cancer_deaths', 0)
            }
            results_summary.append(summary)
            print(f"✅ {strategy_name} 完成")
            
        except Exception as e:
            print(f"❌ 策略 {strategy_name} 运行失败: {e}")

    # 5. 分析和验证结果
    print("\n=== 结果验证 ===")
    
    if results_summary:
        df = pd.DataFrame(results_summary)
        
        # 显示详细结果表格
        print("\n详细策略比较结果:")
        print("=" * 120)
        print(f"{'策略':<15} {'总成本':<12} {'筛查成本':<12} {'治疗成本':<12} {'预防病例':<10} {'筛查次数':<10} {'成本/QALY':<12}")
        print("-" * 120)
        
        for _, row in df.iterrows():
            print(f"{row['Strategy']:<15} "
                  f"¥{row['Total_Cost']:<11,.0f} "
                  f"¥{row['Screening_Cost']:<11,.0f} "
                  f"¥{row['Treatment_Cost']:<11,.0f} "
                  f"{row['Cancer_Cases_Prevented']:<10.0f} "
                  f"{row['Total_Screenings']:<10.0f} "
                  f"¥{row['Cost_per_QALY']:<11,.0f}")

        # 验证关键指标
        print("\n=== 验证检查 ===")
        
        # 1. 验证成本与筛查频率的关系
        print("1. 成本与筛查频率关系验证:")
        df_sorted_by_screenings = df.sort_values('Total_Screenings', ascending=False)
        
        for i, (_, row) in enumerate(df_sorted_by_screenings.iterrows()):
            print(f"   {i+1}. {row['Strategy']}: {row['Total_Screenings']:.0f}次筛查, 总成本¥{row['Total_Cost']:,.0f}")
        
        # 检查筛查次数最多的是否成本最高
        max_screening_strategy = df_sorted_by_screenings.iloc[0]
        min_screening_strategy = df_sorted_by_screenings.iloc[-1]
        
        if max_screening_strategy['Total_Cost'] > min_screening_strategy['Total_Cost']:
            print("   ✅ 筛查次数越多，总成本越高")
        else:
            print("   ❌ 成本与筛查频率关系异常")
        
        # 2. 验证预防效果与筛查频率的关系
        print("\n2. 预防效果与筛查频率关系验证:")
        correlation = df['Total_Screenings'].corr(df['Cancer_Cases_Prevented'])
        print(f"   筛查次数与预防病例数相关系数: {correlation:.3f}")
        
        if correlation > 0.5:
            print("   ✅ 筛查次数越多，预防病例越多")
        else:
            print("   ⚠️ 筛查频率与预防效果相关性较弱")
        
        # 3. 验证成本效益
        print("\n3. 成本效益验证:")
        df_sorted_by_cost_effectiveness = df.sort_values('Cost_per_QALY')
        
        print("   成本效益排序（从最优到最差）:")
        for i, (_, row) in enumerate(df_sorted_by_cost_effectiveness.iterrows()):
            print(f"   {i+1}. {row['Strategy']}: ¥{row['Cost_per_QALY']:,.0f}/QALY")
        
        # 4. 验证数值差异
        print("\n4. 数值差异验证:")
        cost_range = df['Total_Cost'].max() - df['Total_Cost'].min()
        screening_range = df['Total_Screenings'].max() - df['Total_Screenings'].min()
        prevention_range = df['Cancer_Cases_Prevented'].max() - df['Cancer_Cases_Prevented'].min()
        
        print(f"   总成本差异: ¥{cost_range:,.0f}")
        print(f"   筛查次数差异: {screening_range:.0f}次")
        print(f"   预防病例差异: {prevention_range:.0f}例")
        
        # 综合评估
        print("\n=== 综合评估 ===")
        
        checks_passed = 0
        total_checks = 4
        
        if cost_range > 100000:  # 成本差异超过10万
            print("✅ 成本差异显著")
            checks_passed += 1
        else:
            print("❌ 成本差异不足")
        
        if screening_range > 1000:  # 筛查次数差异超过1000次
            print("✅ 筛查频率差异显著")
            checks_passed += 1
        else:
            print("❌ 筛查频率差异不足")
        
        if prevention_range > 50:  # 预防病例差异超过50例
            print("✅ 预防效果差异显著")
            checks_passed += 1
        else:
            print("❌ 预防效果差异不足")
        
        if correlation > 0.3:  # 相关性超过0.3
            print("✅ 筛查频率与预防效果正相关")
            checks_passed += 1
        else:
            print("❌ 筛查频率与预防效果相关性不足")
        
        print(f"\n验证结果: {checks_passed}/{total_checks} 项检查通过")
        
        if checks_passed >= 3:
            print("🎉 筛查间隔修复成功！不同间隔产生了合理的差异化结果")
        else:
            print("⚠️ 筛查间隔修复部分成功，仍有改进空间")

    else:
        print("❌ 没有成功的测试结果")

    print("\n=== 验证完成 ===")


if __name__ == '__main__':
    try:
        validate_screening_intervals()
    except Exception as e:
        print(f"验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

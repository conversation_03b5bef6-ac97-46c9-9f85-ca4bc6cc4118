#!/usr/bin/env python3
"""
筛查配置加载功能测试脚本
演示如何使用Excel和JSON格式的配置文件
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ccsm.core.model import ColorectalCancerMicrosimulationModel, ModelConfiguration
from src.ccsm.modules.screening import ScreeningStrategy, ScreeningToolConfig, ScreeningTool


def test_config_loading():
    """测试配置文件加载功能"""
    print("=" * 60)
    print("筛查配置加载功能测试")
    print("=" * 60)
    
    # 测试1: 创建配置模板
    print("\n1. 创建配置模板文件...")
    model = ColorectalCancerMicrosimulationModel(initial_population=1000)
    model.create_screening_config_templates()
    
    # 测试2: 从配置文件加载
    print("\n2. 从配置文件加载筛查参数和策略...")
    config = ModelConfiguration(
        initial_population=1000,
        load_config_from_files=True,
        config_dir="config",
        screening_params_file="screening_parameters.json",
        screening_strategies_file="screening_strategies.xlsx"
    )
    
    model_with_config = ColorectalCancerMicrosimulationModel(config=config)
    
    # 显示加载的策略
    strategies = model_with_config.list_available_strategies()
    print(f"✅ 成功加载 {len(strategies)} 个筛查策略:")
    for strategy_name in strategies:
        print(f"   - {strategy_name}")
    
    # 测试3: 运行时加载配置
    print("\n3. 测试运行时加载配置...")
    model_runtime = ColorectalCancerMicrosimulationModel(initial_population=1000)
    
    print("加载前的策略数量:", len(model_runtime.list_available_strategies()))
    
    model_runtime.load_screening_config_from_files(
        params_file="config/screening_parameters.json",
        strategies_file="config/screening_strategies.xlsx"
    )
    
    print("加载后的策略数量:", len(model_runtime.list_available_strategies()))
    
    # 测试4: 添加自定义策略并保存
    print("\n4. 添加自定义策略并保存配置...")
    
    # 创建一个自定义策略
    custom_strategy = ScreeningStrategy(
        name="test_custom_strategy",
        tool_configs=[
            ScreeningToolConfig(
                tool=ScreeningTool.FIT,
                start_age=45,
                end_age=80,
                interval=1.5,
                compliance_rate=0.75,
                follow_up_compliance_rate=0.80
            )
        ],
        sequential=False,
        risk_stratified=False
    )
    
    model_runtime.add_screening_strategy(custom_strategy)
    print(f"✅ 已添加自定义策略: {custom_strategy.name}")
    
    # 保存配置
    model_runtime.save_screening_config_to_files(
        params_file="config/test_screening_parameters.json",
        strategies_file="config/test_screening_strategies.xlsx"
    )
    
    # 测试5: 验证保存的配置
    print("\n5. 验证保存的配置...")
    
    test_config = ModelConfiguration(
        initial_population=1000,
        load_config_from_files=True,
        config_dir="config",
        screening_params_file="test_screening_parameters.json",
        screening_strategies_file="test_screening_strategies.xlsx"
    )
    
    model_test = ColorectalCancerMicrosimulationModel(config=test_config)
    test_strategies = model_test.list_available_strategies()
    
    if "test_custom_strategy" in test_strategies:
        print("✅ 自定义策略已成功保存和加载")
    else:
        print("❌ 自定义策略保存或加载失败")
    
    # 测试6: 显示筛查参数
    print("\n6. 显示加载的筛查参数...")
    params = model_with_config.screening_module.params
    
    print("FIT敏感性 (低风险腺瘤):", params.sensitivity[ScreeningTool.FIT].get('LOW_RISK_ADENOMA', 'N/A'))
    print("FIT特异性:", params.specificity.get(ScreeningTool.FIT, 'N/A'))
    print("FIT依从性:", params.compliance.get(ScreeningTool.FIT, 'N/A'))
    print("FIT成本:", params.costs.get(ScreeningTool.FIT, 'N/A'))
    
    print("\n=" * 60)
    print("配置加载功能测试完成")
    print("=" * 60)


def test_strategy_details():
    """测试策略详细信息"""
    print("\n" + "=" * 60)
    print("筛查策略详细信息测试")
    print("=" * 60)
    
    # 加载配置
    config = ModelConfiguration(
        initial_population=1000,
        load_config_from_files=True,
        config_dir="config",
        screening_params_file="screening_parameters.json",
        screening_strategies_file="screening_strategies.xlsx"
    )
    
    model = ColorectalCancerMicrosimulationModel(config=config)
    
    # 显示每个策略的详细信息
    for strategy_name in model.list_available_strategies():
        if strategy_name.startswith('custom_'):  # 只显示自定义策略
            strategy = model.screening_module.get_strategy(strategy_name)
            if strategy:
                print(f"\n策略: {strategy.name}")
                print(f"  贯序筛查: {strategy.sequential}")
                print(f"  风险分层: {strategy.risk_stratified}")
                print(f"  高风险间隔: {strategy.high_risk_interval}")
                
                if strategy.tool_configs:
                    print("  工具配置:")
                    for i, config in enumerate(strategy.tool_configs, 1):
                        print(f"    {i}. {config.tool.value}")
                        print(f"       年龄范围: {config.start_age}-{config.end_age}")
                        print(f"       间隔: {config.interval}年")
                        print(f"       依从性: {config.compliance_rate}")
                        print(f"       随访依从性: {config.follow_up_compliance_rate}")


if __name__ == "__main__":
    try:
        test_config_loading()
        test_strategy_details()
        
        print("\n🎉 所有测试完成！")
        print("\n配置文件位置:")
        print("  - config/screening_parameters.json")
        print("  - config/screening_strategies.xlsx")
        print("  - config/test_screening_parameters.json")
        print("  - config/test_screening_strategies.xlsx")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

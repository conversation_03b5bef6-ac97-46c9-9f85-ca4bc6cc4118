#!/usr/bin/env python3
"""
统一配置加载器测试脚本
测试所有数据加载功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.ccsm.modules.config_loader import UnifiedConfigManager
from src.ccsm.core.model import ColorectalCancerMicrosimulationModel, ModelConfiguration


def test_unified_config_loader():
    """测试统一配置加载器的所有功能"""
    print("=" * 60)
    print("统一配置加载器功能测试")
    print("=" * 60)
    
    # 初始化配置管理器
    config_manager = UnifiedConfigManager("config")
    
    # 测试1: 加载筛查配置
    print("\n1. 测试筛查配置加载...")
    params, strategies = config_manager.initialize_from_config()
    print(f"✅ 加载筛查参数: {params is not None}")
    print(f"✅ 加载筛查策略: {len(strategies)} 个")
    
    # 测试2: 加载基准数据
    print("\n2. 测试基准数据加载...")
    benchmark_data = config_manager.load_benchmark_data()
    print(f"✅ 基准数据类型: {list(benchmark_data.keys())}")
    
    # 测试3: 加载寿命表数据
    print("\n3. 测试寿命表数据加载...")
    lifetable_data = config_manager.load_lifetable_data()
    print(f"✅ 寿命表数据包含性别: {list(lifetable_data.keys())}")
    for gender, data in lifetable_data.items():
        if data:
            print(f"   - {gender}: {len(data)} 个年龄组")
    
    # 测试4: 加载人口构成数据
    print("\n4. 测试人口构成数据加载...")
    population_data = config_manager.load_population_data()
    print(f"✅ 人口数据包含: {list(population_data.keys())}")
    print(f"   - 年龄分布: {len(population_data.get('age_distribution', {}))} 个年龄组")
    print(f"   - 性别比例: {population_data.get('gender_ratio', 'N/A')}")
    print(f"   - 总人口: {population_data.get('total_population', 'N/A'):,}")
    
    # 测试5: 加载筛查性能数据
    print("\n5. 测试筛查性能数据加载...")
    screening_data = config_manager.load_screening_data()
    print(f"✅ 筛查性能数据: {list(screening_data.keys())}")
    
    # 测试6: 一次性加载所有数据
    print("\n6. 测试一次性加载所有数据...")
    all_data = config_manager.load_all_data()
    print(f"✅ 所有数据类型: {list(all_data.keys())}")
    
    # 测试7: 与模型集成测试
    print("\n7. 测试与模型集成...")
    config = ModelConfiguration(
        initial_population=1000,
        load_config_from_files=True,
        config_dir="config"
    )
    
    model = ColorectalCancerMicrosimulationModel(config=config)
    print(f"✅ 模型初始化成功，策略数量: {len(model.list_available_strategies())}")
    
    # 测试8: 数据文件路径验证
    print("\n8. 验证数据文件路径...")
    config_dir = Path("config")
    expected_files = [
        "screening_parameters.json",
        "screening_strategies.xlsx",
        "default_benchmarks.xlsx",
        "lifetable.xlsx",
        "population.xlsx"
    ]
    
    for file_name in expected_files:
        file_path = config_dir / file_name
        status = "✅" if file_path.exists() else "❌"
        print(f"   {status} {file_name}")
    
    print("\n=" * 60)
    print("统一配置加载器测试完成")
    print("=" * 60)


def test_custom_lifetable():
    """测试自定义生命表加载功能"""
    print("\n" + "=" * 60)
    print("自定义生命表加载测试")
    print("=" * 60)
    
    config_manager = UnifiedConfigManager("config")
    
    # 测试加载现有的生命表文件
    print("\n测试加载现有生命表文件...")
    try:
        lifetable_data = config_manager.load_custom_lifetable("lifetable.xlsx")
        print(f"✅ 成功加载生命表数据")
        for gender, data in lifetable_data.items():
            if data:
                print(f"   - {gender}: {len(data)} 个年龄组")
    except Exception as e:
        print(f"❌ 加载失败: {e}")


def test_data_integration():
    """测试数据整合功能"""
    print("\n" + "=" * 60)
    print("数据整合功能测试")
    print("=" * 60)
    
    # 创建配置，启用所有数据加载
    config = ModelConfiguration(
        initial_population=10000,
        load_config_from_files=True,
        config_dir="config"
    )
    
    print("\n初始化模型并加载所有配置...")
    model = ColorectalCancerMicrosimulationModel(config=config)
    
    print(f"\n模型状态:")
    print(f"✅ 初始人口: {config.initial_population:,}")
    print(f"✅ 可用筛查策略: {len(model.list_available_strategies())}")
    print(f"✅ 配置管理器类型: {type(model.config_manager).__name__}")
    
    # 测试配置管理器的数据加载功能
    print(f"\n配置管理器功能测试:")
    all_data = model.config_manager.load_all_data()
    
    for data_type, data in all_data.items():
        if data:
            if isinstance(data, dict):
                print(f"✅ {data_type}: {len(data)} 项")
            elif isinstance(data, list):
                print(f"✅ {data_type}: {len(data)} 个")
            else:
                print(f"✅ {data_type}: 已加载")
        else:
            print(f"❌ {data_type}: 未加载")


if __name__ == "__main__":
    try:
        test_unified_config_loader()
        test_custom_lifetable()
        test_data_integration()
        
        print("\n🎉 所有测试完成！")
        print("\n统一配置加载器功能:")
        print("✅ 筛查参数和策略配置加载")
        print("✅ 基准数据加载")
        print("✅ 寿命表数据加载")
        print("✅ 人口构成数据加载")
        print("✅ 筛查性能数据加载")
        print("✅ 自定义生命表加载")
        print("✅ 与主模型完全集成")
        print("✅ 所有数据文件统一到config目录")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
